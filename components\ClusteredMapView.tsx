import React, { forwardRef, useMemo } from 'react';
import { StyleSheet, View } from "react-native";
import MapView, { LatLng, MapPressEvent, Marker, PROVIDER_GOOGLE, Region, Polygon } from 'react-native-maps';
import { Surface, Text, useTheme } from 'react-native-paper';
import { CustomPlace, Place } from '../models/place';
import PlaceMarker from './PlaceMarker';
import { CIRCLE_DIAMETER_IN_METERS } from '../utils/constants';
import { PlaceType, placeTypeConf } from '../models/constants';
import Supercluster, { AnyProps } from 'supercluster';
import circle from '@turf/circle';
import union from '@turf/union';
import type { Feature, Polygon as GeoJSONPolygon, MultiPolygon as GeoJSONMultiPolygon, GeoJSON } from 'geojson';

interface Props {
    readonly style: any;
    readonly initialRegion: Region;
    readonly places: ReadonlyArray<Place | CustomPlace>;
    readonly selectedPlace?: Place | CustomPlace;
    readonly clusters: Array<Supercluster.ClusterFeature<AnyProps>>;
    readonly showUserLocation: boolean;
    readonly addingMarker: boolean;
    readonly onClusterMarkerPress: (clusterMarkerCoordinates: LatLng) => void;
    readonly onMarkerPress: (place: Place | CustomPlace) => void;
    readonly onMapLoaded: () => void;
    readonly onRegionChangeComplete: (newRegion: Region, details: any) => void;
    readonly onPress: (event: MapPressEvent) => void;
}

const ClusteredMapView = forwardRef<MapView, Props>(({
    style,
    initialRegion,
    places,
    clusters,
    selectedPlace,
    showUserLocation,
    addingMarker,
    onMarkerPress,
    onClusterMarkerPress,
    onMapLoaded,
    onRegionChangeComplete,
    onPress
}, ref) => {

    const theme = useTheme();

    // ===== Compute union outline for existing pharmacies (excluding hidden) =====
    const existingPharmaciesUnionPolygons = useMemo(() => {
        try {
            const existing = places.filter(p => p.type === PlaceType.EXISTING_PHARMACY && !p.tags?.includes('HIDDEN')) as Place[];
            if (existing.length === 0) return [] as Array<Array<{ latitude: number; longitude: number }>>;

            // Build Turf circles in meters. Turf expects [lng, lat]
            const circles: Feature<GeoJSONPolygon | GeoJSONMultiPolygon>[] = existing.map(p =>
                circle([p.geometry.lng, p.geometry.lat], CIRCLE_DIAMETER_IN_METERS, { units: 'meters', steps: 32 })
            );

            // Handle single circle case or compute union for multiple circles
            let unionGeom: GeoJSON | null = null;
            if (circles.length === 1) {
                unionGeom = circles[0];
            } else if (circles.length > 1) {
                // Reduce to a single union geometry
                unionGeom = circles[0];
                for (let i = 1; i < circles.length; i++) {
                    if (unionGeom && circles[i]) {
                        unionGeom = union(unionGeom as any, circles[i]) as any;
                    }
                }
            }
            if (!unionGeom) return [];

            const features: Feature[] = (unionGeom as any).type === 'FeatureCollection'
                ? (unionGeom as any).features
                : [(unionGeom as any)];

            const polygonsLatLng: Array<Array<{ latitude: number; longitude: number }>> = [];
            for (const f of features) {
                if (!f.geometry) continue;
                if (f.geometry.type === 'Polygon') {
                    const coords = f.geometry.coordinates[0] || [];
                    polygonsLatLng.push(coords.map(([lng, lat]) => ({ latitude: lat, longitude: lng })));
                } else if (f.geometry.type === 'MultiPolygon') {
                    for (const poly of f.geometry.coordinates) {
                        const coords = poly[0] || [];
                        polygonsLatLng.push(coords.map(([lng, lat]) => ({ latitude: lat, longitude: lng })));
                    }
                }
            }
            return polygonsLatLng;
        } catch (e) {
            console.warn('Failed to compute union for existing pharmacies:', e);
            return [] as Array<Array<{ latitude: number; longitude: number }>>;
        }
    }, [places]);

    const renderCluster = (cluster: Supercluster.ClusterFeature<AnyProps>) => {
        const { id, geometry, properties } = cluster;
        const { point_count } = properties;
        const [longitude, latitude] = geometry.coordinates;

        return (
            <Marker
                key={`cluster-${id}`}
                coordinate={{ latitude, longitude }}
                onPress={() => onClusterMarkerPress({ latitude, longitude })}
            >
                <View style={styles.clusterContainer}>
                    <Text style={styles.clusterText}>{point_count}</Text>
                </View>
            </Marker>
        );
    };

    const renderPlaceMarker = (place: Place | CustomPlace) => {
        const { style } = placeTypeConf[place.type];
        return (
            <PlaceMarker
                key={`${place.placeId}-${place.geometry.lat},${place.geometry.lng}`}
                place={place}
                selected={selectedPlace?.placeId === place.placeId}
                showCircle={(!place.tags?.includes("HIDDEN") || PlaceType.SEARCH_RESULT === place.type)}
                markerColor={style.color}
                circleDiameter={CIRCLE_DIAMETER_IN_METERS}
                fillColor={style.fillColor}
                strokeColor={style.strokeColor}
                onPress={() => {
                    if (addingMarker) {
                        return;
                    }
                    onMarkerPress(place);
                }}
            />
        );
    };

    const unionStroke = placeTypeConf[PlaceType.EXISTING_PHARMACY].style.strokeColor;

    return (
        <MapView
            ref={ref}
            style={style}
            provider={PROVIDER_GOOGLE}
            mapType="hybrid"
            showsCompass={true}
            showsUserLocation={showUserLocation}
            showsMyLocationButton={false}
            rotateEnabled={true}
            toolbarEnabled={false}
            onCalloutPress={() => { }}
            initialRegion={initialRegion}
            onMapLoaded={onMapLoaded}
            onPress={onPress}
            onRegionChangeComplete={onRegionChangeComplete}
        >
            {/* Union outline for existing pharmacies (no fill) */}
            {existingPharmaciesUnionPolygons.map((coords, idx) => (
                <Polygon
                    key={`pharm-union-${idx}`}
                    coordinates={coords}
                    strokeColor={unionStroke}
                    strokeWidth={4}
                    tappable={false}
                    fillColor={"rgba(0,0,0,0)"}
                    zIndex={1}
                />
            ))}

            {clusters.map((cluster: Supercluster.ClusterFeature<AnyProps>) => {
                if (cluster.properties.cluster) {
                    return renderCluster(cluster);
                } else {
                    const place: Place | CustomPlace | undefined = places.find(currentPlace => currentPlace.placeId === cluster.properties.placeId);
                    if (!place) {
                        return null;
                    } else {
                        return renderPlaceMarker(place);
                    }
                }
            })}
        </MapView>
    );
});

const styles = StyleSheet.create({
    clusterContainer: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'rgba(0, 150, 136, 0.7)',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 2,
        borderColor: '#fff',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.8,
        shadowRadius: 2,
        elevation: 5,
    },
    clusterText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default ClusteredMapView;
