import { Calendar, getCalendars } from 'expo-localization';
import { getAuth } from 'firebase/auth';
import { Platform } from 'react-native';
import { getDeviceTimezone, requireNonNull } from '../../utils/others';
import { getOrCreateDeviceId } from '../deviceIdService';
import { AUTHORIZATION_HEADER, DEVICE_ID_HEADER, DEVICE_OS_HEADER, TIMEZONE_HEADER } from './constants';

const API_URL: string = requireNonNull(process.env.EXPO_PUBLIC_API_URL);

const getAuthToken = async (): Promise<string | undefined> => {
    const idToken = await getAuth().currentUser?.getIdToken();
    return idToken ? `Bearer ${idToken}` : undefined;
};

const makeRequest = async (url: string, method: string, data?: object, headers?: Map<string, string>): Promise<Response> => {
    const token: string | undefined = await getAuthToken();

    const finalHeaders: Headers = new Headers();
    if (token) {
        finalHeaders.append(AUTHORIZATION_HEADER, token);
    }

    const currentTimezone: string | null = getDeviceTimezone();
    if (currentTimezone) {
        finalHeaders.append(TIMEZONE_HEADER, currentTimezone);
    }

    const deviceId: string | null = await getOrCreateDeviceId();
    if (deviceId) {
        finalHeaders.append(DEVICE_ID_HEADER, deviceId);
    }

    const deviceOS: string | null = Platform.OS;
    if (deviceOS) {
        finalHeaders.append(DEVICE_OS_HEADER, deviceOS);
    }

    headers?.forEach((value: string, key: string) => {
        finalHeaders.append(key, value);
    })

    const response = await fetch(`${API_URL}${url}`, {
        method,
        headers: finalHeaders,
        body: data ? JSON.stringify(data) : undefined,
    });

    return response;
};


export async function apiFetchClient(url: string, method: string, headers?: Map<string, string>, data?: object) {
    try {
        return await makeRequest(url, method, data, headers);
    } catch (error) {
        console.error('API request error:', error);
        throw error; // Re-throw the error for handling in the calling code
    }
};
